{"name": "eu-email-webhook", "version": "1.0.0", "description": "EU-compliant email to webhook service", "type": "module", "main": "dist/backend/index.js", "scripts": {"start": "node dist/backend/index.js", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "tsx watch src/backend/index.ts", "dev:frontend": "vite", "build": "npm run build:backend && npm run build:frontend", "build:backend": "tsc", "build:frontend": "vue-tsc --project tsconfig.vue.json --noEmit && vite build", "generate:types": "openapi-typescript http://localhost:3000/docs/json --output src/frontend/types/api-generated.ts", "preview": "vite preview", "test": "jest tests", "test:verify-deployment": "node scripts/verify-deployment.js", "test:unit": "jest tests/unit tests/frontend", "test:integration": "jest tests/integration", "test:webhook": "jest tests/integration/test-webhook-flow.test.ts tests/unit/user-id-suffix.test.ts tests/e2e/test-webhook-user-journey.test.ts", "test:webhook:manual": "node scripts/test-webhook-flow.js", "test:watch": "jest tests --watch", "test:coverage": "jest tests --coverage", "test:e2e": "node tests/emails/remote-e2e-test.js", "test:e2e:auth": "node tests/e2e/auth-deployment.test.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "rm -rf dist/ node_modules/.vite/", "docker:dev": "docker-compose --env-file .env up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker-compose down -v --remove-orphans && docker system prune -f"}, "keywords": ["email", "webhook", "gdpr", "eu-compliant", "docker", "nodejs", "typescript"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@fastify/cookie": "^11.0.2", "@fastify/cors": "^11.0.1", "@fastify/formbody": "^8.0.2", "@fastify/static": "^8.2.0", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "@mollie/api-client": "^4.3.2", "@prisma/client": "^6.10.1", "@tailwindcss/vite": "^4.1.10", "@types/bcrypt": "^5.0.2", "@types/puppeteer": "^5.4.7", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "bcrypt": "^6.0.0", "bull": "^4.16.5", "cssnano": "^7.0.7", "daisyui": "^5.0.43", "dotenv": "^16.5.0", "fastify": "^5.4.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mailparser": "^3.7.3", "nodemailer": "^7.0.3", "pinia": "^3.0.3", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "postcss": "^8.5.5", "postcss-cli": "^11.0.1", "puppeteer": "^24.10.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.10", "uuid": "^11.1.0", "vue": "^3.5.16", "vue-router": "^4.5.1", "zod": "^3.25.64"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.29.0", "@jest/globals": "^30.0.0", "@tailwindcss/cli": "^4.1.10", "@tailwindcss/postcss": "^4.1.10", "@types/jest": "^29.5.14", "@types/mailparser": "^3.4.6", "@types/node": "^24.0.1", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue": "^5.2.4", "@vue/tsconfig": "^0.7.0", "concurrently": "^9.1.2", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "globals": "^16.2.0", "jest": "^30.0.0", "jest-junit": "^16.0.0", "openapi-typescript": "^7.8.0", "prettier": "^3.5.3", "prisma": "^6.10.1", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "tsx": "^4.20.3", "typescript": "^5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.10"}}
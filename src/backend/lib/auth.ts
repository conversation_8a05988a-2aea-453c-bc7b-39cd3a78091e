/**
 * REDESIGNED: Clear, Unambiguous Authentication System
 * 
 * Every action is a "feature" that automatically validates:
 * 1. API scope (if API key used)  
 * 2. Plan permissions (if required)
 * 3. Resource limits (if applicable)
 * 
 * No more ambiguous scope vs feature distinction!
 */

import { FastifyRequest, FastifyReply } from 'fastify';
import { UnifiedPermissionService } from '../services/auth/unified/unified-permission.service.js';
import { UserAuthService } from '../services/auth/user-auth.service.js';
import { ApiKeyService } from '../services/auth/api-key.service.js';
import { AdminAuthService } from '../services/auth/admin-auth.service.js';
import { logger } from '../utils/logger.js';

// Initialize services
const userAuthService = new UserAuthService();
const apiKeyService = new ApiKeyService();
const adminAuthService = new AdminAuthService();

/**
 * CLEAR FEATURE DEFINITIONS
 * Every action is defined as a feature with clear requirements
 *
 * NOTE: These constants now match the actual feature names for consistency
 */
export const FEATURES = {
  // Webhook features
  WEBHOOK_READ: 'webhook:read',           // Read webhooks (all plans)
  WEBHOOK_CREATE: 'webhook:create',       // Create webhook with custom headers (Pro+)
  WEBHOOK_CREATE_BASIC: 'webhook:create_basic', // Create basic webhook (Free plan)
  WEBHOOK_UPDATE: 'webhook:update',       // Update webhook
  WEBHOOK_DELETE: 'webhook:delete',       // Delete webhook
  WEBHOOK_TEST: 'webhook:test',           // Test webhook

  // Domain features
  DOMAIN_READ: 'domain:read',             // Read domains (all plans)
  DOMAIN_CREATE: 'domain:create',         // Create domain (all plans, subject to limits)
  DOMAIN_UPDATE: 'domain:update',         // Update basic domain settings
  DOMAIN_CONFIG: 'domain:config',         // Update advanced config (Pro+)
  DOMAIN_DELETE: 'domain:delete',         // Delete domain
  DOMAIN_VERIFY: 'domain:verify',         // Verify domain

  // Alias features
  ALIAS_READ: 'alias:read',               // Read aliases (all plans)
  ALIAS_CREATE: 'alias:create',           // Create alias (all plans, subject to limits)
  ALIAS_UPDATE: 'alias:update',           // Update alias
  ALIAS_DELETE: 'alias:delete',           // Delete alias

  // Analytics features
  ANALYTICS_READ: 'analytics:read',       // View analytics (Enterprise)

  // Profile/account features
  PROFILE_READ: 'profile:read',           // Read own profile
  PROFILE_UPDATE: 'profile:update',       // Update own profile
  APIKEY_READ: 'apikey:read',            // Read own API keys
  APIKEY_CREATE: 'apikey:create',        // Create API keys
  APIKEY_DELETE: 'apikey:delete',        // Delete own API keys

  // Billing features
  BILLING_READ: 'billing:read',          // Read billing info
  BILLING_UPDATE: 'billing:update',     // Update billing/plan

  // Admin features
  ADMIN_ACCESS: 'admin:access'           // Admin panel access
} as const;

/**
 * MAIN AUTHENTICATION FUNCTION
 * Always validates based on feature requirements
 */
export function requireFeature(feature: string, options: { 
  allowUnauthenticated?: boolean;
  adminOnly?: boolean;
} = {}) {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // Handle admin authentication
      if (options.adminOnly) {
        return handleAdminAuth(request, reply);
      }

      // Handle user authentication
      const authResult = await authenticateUser(request);
      
      if (!authResult.success) {
        if (options.allowUnauthenticated) return;
        return sendAuthError(reply, authResult.error!, request);
      }

      // Attach user to request
      (request as any).user = authResult.user;
      (request as any).authMethod = authResult.method;
      (request as any).apiKey = authResult.apiKey;

      // Validate feature permissions
      const permissionResult = await UnifiedPermissionService.validateFeatureAccess({
        userId: authResult.user!.id,
        apiKey: authResult.apiKey,
        featureName: feature
      });
      
      if (!permissionResult.allowed) {
        return sendPermissionError(reply, permissionResult);
      }

      // Attach permission context
      (request as any).permissions = {
        validated: true,
        feature,
        details: permissionResult.details,
        effectivePermissions: await UnifiedPermissionService.getEffectivePermissions(
          authResult.user!.id,
          authResult.apiKey
        )
      };

    } catch (error) {
      logger.error({ error, url: request.url, feature }, 'Authentication error');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Authentication system error'
      });
    }
  };
}

/**
 * CONVENIENCE FUNCTIONS - All based on clear features
 */

// Basic authentication (no specific permissions)
export const requireAuth = () => requireFeature('auth:basic');

// Admin access
export const requireAdmin = () => requireFeature(FEATURES.ADMIN_ACCESS, { adminOnly: true });

// Webhook permissions
export const webhookAuth = {
  read: () => requireFeature(FEATURES.WEBHOOK_READ),
  create: () => requireFeature(FEATURES.WEBHOOK_CREATE),           // Pro+ (custom headers)
  createBasic: () => requireFeature(FEATURES.WEBHOOK_CREATE_BASIC), // Free plan
  update: () => requireFeature(FEATURES.WEBHOOK_UPDATE),
  delete: () => requireFeature(FEATURES.WEBHOOK_DELETE),
  test: () => requireFeature(FEATURES.WEBHOOK_TEST)
};

// Domain permissions
export const domainAuth = {
  read: () => requireFeature(FEATURES.DOMAIN_READ),
  create: () => requireFeature(FEATURES.DOMAIN_CREATE),
  update: () => requireFeature(FEATURES.DOMAIN_UPDATE),
  config: () => requireFeature(FEATURES.DOMAIN_CONFIG),
  delete: () => requireFeature(FEATURES.DOMAIN_DELETE),
  verify: () => requireFeature(FEATURES.DOMAIN_VERIFY)
};

// Alias permissions  
export const aliasAuth = {
  read: () => requireFeature(FEATURES.ALIAS_READ),
  create: () => requireFeature(FEATURES.ALIAS_CREATE),
  update: () => requireFeature(FEATURES.ALIAS_UPDATE),
  delete: () => requireFeature(FEATURES.ALIAS_DELETE)
};

// Profile permissions
export const profileAuth = {
  read: () => requireFeature(FEATURES.PROFILE_READ),
  update: () => requireFeature(FEATURES.PROFILE_UPDATE)
};

// API key permissions
export const apiKeyAuth = {
  read: () => requireFeature(FEATURES.APIKEY_READ),
  create: () => requireFeature(FEATURES.APIKEY_CREATE),
  delete: () => requireFeature(FEATURES.APIKEY_DELETE)
};

// Billing permissions
export const billingAuth = {
  read: () => requireFeature(FEATURES.BILLING_READ),
  update: () => requireFeature(FEATURES.BILLING_UPDATE)
};

// Analytics permissions
export const analyticsAuth = {
  read: () => requireFeature(FEATURES.ANALYTICS_READ)
};

/**
 * ENHANCED CONTEXT-AWARE PERMISSIONS
 * These also validate resource ownership
 */
export function requireResourceAccess(feature: string, resourceType: 'domain' | 'alias' | 'webhook') {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    // First apply feature-based auth
    await requireFeature(feature)(request, reply);
    
    if (reply.sent) return;

    // Then validate resource ownership
    const user = (request as any).user;
    const resourceId = (request.params as any)[`${resourceType}Id`];

    if (!resourceId) {
      return reply.status(400).send({
        statusCode: 400,
        error: 'Bad Request',
        message: `${resourceType} ID is required`
      });
    }

    const ownsResource = await checkResourceOwnership(user.id, resourceType, resourceId);
    if (!ownsResource) {
      return reply.status(404).send({
        statusCode: 404,
        error: 'Not Found',
        message: `${resourceType} not found`
      });
    }
  };
}

/**
 * SMART OWNER AUTH FUNCTIONS
 * These automatically determine the operation based on HTTP method and validate ownership
 */
function createOwnerAuthMiddleware(resourceType: 'domain' | 'alias' | 'webhook') {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    // Determine operation based on HTTP method
    const method = request.method.toLowerCase();
    let operation: string;

    switch (method) {
      case 'get':
        operation = 'read';
        break;
      case 'post':
        operation = resourceType === 'domain' ? 'verify' : 'create';
        break;
      case 'put':
      case 'patch':
        operation = 'update';
        break;
      case 'delete':
        operation = 'delete';
        break;
      default:
        operation = 'read';
    }

    // Get the appropriate feature name
    const featureName = `${resourceType}:${operation}`;

    // Apply feature-based auth first
    await requireFeature(featureName)(request, reply);

    if (reply.sent) return;

    // Then validate resource ownership
    const user = (request as any).user;
    const resourceId = (request.params as any)[`${resourceType}Id`];

    if (!resourceId) {
      return reply.status(400).send({
        statusCode: 400,
        error: 'Bad Request',
        message: `${resourceType} ID is required`
      });
    }

    // Check ownership
    const ownsResource = await checkResourceOwnership(user.id, resourceType, resourceId);
    if (!ownsResource) {
      return reply.status(404).send({
        statusCode: 404,
        error: 'Not Found',
        message: `${resourceType} not found`
      });
    }
  };
}

/**
 * Helper function to check resource ownership
 */
async function checkResourceOwnership(
  userId: string,
  resourceType: string,
  resourceId: string
): Promise<boolean> {
  try {
    const { PrismaClient } = await import('@prisma/client');
    const prisma = new PrismaClient();

    let query: any;
    switch (resourceType) {
      case 'domain':
        query = prisma.domain.findFirst({
          where: { id: resourceId, userId },
          select: { id: true }
        });
        break;
      case 'webhook':
        query = prisma.webhook.findFirst({
          where: { id: resourceId, userId },
          select: { id: true }
        });
        break;
      case 'alias':
        query = prisma.alias.findFirst({
          where: {
            id: resourceId,
            domain: { userId }
          },
          select: { id: true }
        });
        break;
      default:
        return false;
    }

    const resource = await query;
    await prisma.$disconnect();

    return !!resource;
  } catch (error) {
    logger.error({ error, userId, resourceType, resourceId }, 'Error checking resource ownership');
    return false;
  }
}

// Context-aware resource permissions
export const webhookOwnerAuth = createOwnerAuthMiddleware('webhook');
export const domainOwnerAuth = createOwnerAuthMiddleware('domain');
export const aliasOwnerAuth = createOwnerAuthMiddleware('alias');

/**
 * HELPER FUNCTIONS (same as before)
 */

async function authenticateUser(request: FastifyRequest) {
  const apiKey = request.headers['x-api-key'] as string;
  
  if (apiKey) {
    const result = await apiKeyService.verifyApiKey(apiKey);
    if (result.success) {
      return {
        success: true,
        user: result.user!,
        method: 'api_key' as const,
        apiKey
      };
    }
    return { success: false, error: 'Invalid API key' };
  }

  const authHeader = request.headers.authorization as string;
  const bearerToken = authHeader?.startsWith('Bearer ') ? authHeader.slice(7) : null;
  const cookieToken = request.cookies.user_token;
  const token = bearerToken || cookieToken;

  if (!token) {
    return { success: false, error: 'No authentication provided' };
  }

  const verifyResult = userAuthService.verifyToken(token);
  if (!verifyResult.success) {
    return { success: false, error: 'Invalid session token' };
  }

  try {
    const { PrismaClient } = await import('@prisma/client');
    const prisma = new PrismaClient();

    const user = await prisma.user.findUnique({
      where: { id: verifyResult.payload!.userId },
      select: { id: true, email: true, planType: true }
    });

    await prisma.$disconnect();

    if (!user) {
      return { success: false, error: 'User not found' };
    }

    return {
      success: true,
      user,
      method: 'session' as const
    };
  } catch (error) {
    logger.error({ error }, 'Database error during authentication');
    return { success: false, error: 'Database error' };
  }
}

async function handleAdminAuth(request: FastifyRequest, reply: FastifyReply) {
  const token = request.cookies.admin_token;

  if (!token) {
    if (request.url.startsWith('/admin/') && request.url !== '/admin/login' && request.method !== 'POST') {
      return reply.status(302).redirect('/admin/login');
    }
    return reply.status(401).send({ error: 'Admin authentication required' });
  }

  const verifyResult = adminAuthService.verifyToken(token);
  if (!verifyResult.success) {
    if (request.url.startsWith('/admin/') && request.url !== '/admin/login' && request.method !== 'POST') {
      reply.clearCookie('admin_token', { path: '/' });
      return reply.status(302).redirect('/admin/login');
    }
    return reply.status(401).send({ error: 'Invalid admin token' });
  }
}

function sendAuthError(reply: FastifyReply, error: string, request: FastifyRequest) {
  if (request.url.startsWith('/domains') && request.method !== 'POST') {
    return reply.status(302).redirect('/login');
  }
  return reply.status(401).send({
    statusCode: 401,
    error: 'Unauthorized',
    message: error
  });
}

function sendPermissionError(reply: FastifyReply, result: any) {
  const { details } = result;
  const statusCode = details.upgradeRequired ? 402 : 403;

  let errorType: string;
  let message: string;

  if (!details.hasApiScope) {
    errorType = 'scope_missing';
    message = details.scopeReason || 'Insufficient API scope';
  } else if (!details.hasPlanPermission) {
    errorType = 'plan_permission_missing';
    message = details.planPermissionReason || 'Plan upgrade required';
  } else if (!details.withinLimits) {
    errorType = 'limit_exceeded';
    message = details.limitReason || 'Resource limit exceeded';
  } else {
    errorType = 'feature_not_available';
    message = result.reason || 'Feature not available';
  }

  return reply.status(statusCode).send({
    statusCode,
    error: statusCode === 402 ? 'Payment Required' : 'Forbidden',
    message,
    success: false,
    details: {
      errorType,
      hasApiScope: details.hasApiScope,
      hasPlanPermission: details.hasPlanPermission,
      withinLimits: details.withinLimits,
      currentPlan: details.currentPlan,
      requiredPlan: details.requiredPlan,
      upgradeRequired: details.upgradeRequired,
      currentUsage: details.currentUsage,
      limits: details.limits
    },
    upgradeUrl: details.upgradeUrl
  });
}



// Legacy handler exports (only for login/register endpoints)
export async function userRegisterHandler(request: FastifyRequest, reply: FastifyReply) {
  let userData;
  if (request.headers['content-type']?.includes('application/json')) {
    userData = request.body as { email?: string; password?: string; name?: string };
  } else {
    userData = request.body as any;
  }

  if (!userData.email || !userData.password) {
    return reply.status(400).send({ error: 'Email and password are required' });
  }

  const result = await userAuthService.registerUser({
    email: userData.email,
    password: userData.password,
    name: userData.name
  });

  if (!result.success) {
    const statusCode = result.error?.includes('required') ||
                      result.error?.includes('Invalid email') ||
                      result.error?.includes('Password must') ? 400 :
                      result.error?.includes('already exists') ? 409 : 500;
    return reply.status(statusCode).send({ error: result.error });
  }

  const cookieConfig = userAuthService.getCookieConfig();
  reply.setCookie('user_token', result.token!, cookieConfig);

  if (!request.headers['content-type']?.includes('application/json')) {
    return reply.status(302).redirect('/domains');
  }

  return reply.status(201).send({
    message: 'Registration successful',
    user: result.user,
    token: result.token
  });
}

export async function userLoginHandler(request: FastifyRequest, reply: FastifyReply) {
  const { email, password } = request.body as { email?: string; password?: string };

  if (!email || !password) {
    return reply.status(400).send({ error: 'Email and password are required' });
  }

  const result = await userAuthService.loginUser({ email, password });

  if (!result.success) {
    const statusCode = result.error?.includes('required') ? 400 :
                      result.error?.includes('Invalid credentials') ? 401 : 500;
    return reply.status(statusCode).send({ error: result.error });
  }

  const cookieConfig = userAuthService.getCookieConfig();
  reply.setCookie('user_token', result.token!, cookieConfig);

  return reply.send({
    message: 'Login successful',
    user: result.user
  });
}

export async function adminLoginHandler(request: FastifyRequest, reply: FastifyReply) {
  const { username, password } = request.body as { username?: string; password?: string };

  if (!username || !password) {
    return reply.status(400).send({ error: 'Username and password are required' });
  }

  const authResult = await adminAuthService.authenticateAdmin({ username, password });
  if (!authResult.success) {
    const statusCode = authResult.error?.includes('required') ? 400 :
                      authResult.error?.includes('Invalid credentials') ? 401 : 500;
    return reply.status(statusCode).send({ error: authResult.error });
  }

  const tokenResult = adminAuthService.generateToken();
  if (!tokenResult.success) {
    return reply.status(500).send({ error: tokenResult.error });
  }

  const cookieConfig = adminAuthService.getCookieConfig();
  reply.setCookie('admin_token', tokenResult.token!, cookieConfig);

  return reply.send({ message: 'Admin login successful', token: tokenResult.token });
}

// Export usage utility functions
export async function checkUserUsageLimit(userId: string): Promise<boolean> {
  return userAuthService.checkUserUsageLimit(userId);
}

export async function incrementUserEmailUsage(userId: string): Promise<void> {
  return userAuthService.incrementUserEmailUsage(userId);
}

/**
 * LEGACY COMPATIBILITY EXPORTS
 * These are maintained for backward compatibility with existing route files
 */

// Legacy middleware exports
export const userOrApiKeyAuthMiddleware = requireAuth();
export const adminAuthMiddleware = requireAdmin();

// Smart auth exports (re-exported from enhanced-auth.middleware.ts)
export { smartWebhookAuth, smartDomainAuth } from '../middleware/enhanced-auth.middleware.js';

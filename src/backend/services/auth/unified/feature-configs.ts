/**
 * UPDATED: Feature Configuration - Single Source of Truth
 * 
 * This is where ALL feature requirements are maintained in one place.
 * Easy to check, easy to maintain, clear mapping of requirements.
 */

import { FeatureConfig } from '../../../types/permissions.types.js';

/**
 * FEATURE REQUIREMENTS MATRIX
 * 
 * This is the single source of truth for what each feature requires:
 * - requiredScope: What API scope is needed
 * - requiredPlanPermissions: What plan permissions are needed  
 * - resourceLimits: What resource limits apply
 * - minimumPlan: Minimum plan required (if any)
 * 
 * Adding a new feature? Just add it here!
 */
export const FEATURE_CONFIGS: Record<string, FeatureConfig> = {
  // ============================================================================
  // WEBHOOK FEATURES
  // ============================================================================
  'webhook:read': {
    requiredScope: 'webhooks:read',
    requiredPlanPermissions: [],
    description: 'Read webhook information - available on all plans'
  },
  
  'webhook:create': {
    requiredScope: 'webhooks:write',
    requiredPlanPermissions: ['custom_headers'],
    resourceLimits: { resourceType: 'webhooks', operation: 'create' },
    minimumPlan: 'pro',
    description: 'Create webhook with custom headers - requires Pro+ plan'
  },
  
  'webhook:create_basic': {
    requiredScope: 'webhooks:write',
    requiredPlanPermissions: [],
    resourceLimits: { resourceType: 'webhooks', operation: 'create' },
    description: 'Create basic webhook without custom headers - available on Free plan'
  },
  
  'webhook:update': {
    requiredScope: 'webhooks:write',
    requiredPlanPermissions: [],
    description: 'Update webhook - custom headers require Pro+ plan (validated in controller)'
  },
  
  'webhook:delete': {
    requiredScope: 'webhooks:write',
    requiredPlanPermissions: [],
    description: 'Delete webhook - available on all plans'
  },
  
  'webhook:test': {
    requiredScope: 'webhooks:write',
    requiredPlanPermissions: [],
    description: 'Test webhook - available on all plans'
  },

  // ============================================================================
  // DOMAIN FEATURES  
  // ============================================================================
  'domain:read': {
    requiredScope: 'domains:read',
    requiredPlanPermissions: [],
    description: 'Read domain information - available on all plans'
  },
  
  'domain:create': {
    requiredScope: 'domains:write',
    requiredPlanPermissions: [],
    resourceLimits: { resourceType: 'domains', operation: 'create' },
    description: 'Create domain - available on all plans (subject to quantity limits)'
  },
  
  'domain:update': {
    requiredScope: 'domains:write',
    requiredPlanPermissions: [],
    description: 'Update basic domain settings - available on all plans'
  },
  
  'domain:config': {
    requiredScope: 'domains:config',
    requiredPlanPermissions: ['custom_headers'],
    minimumPlan: 'pro',
    description: 'Update advanced domain configuration - requires Pro+ plan'
  },
  
  'domain:delete': {
    requiredScope: 'domains:write',
    requiredPlanPermissions: [],
    description: 'Delete domain - available on all plans'
  },
  
  'domain:verify': {
    requiredScope: 'domains:write',
    requiredPlanPermissions: [],
    description: 'Verify domain - available on all plans'
  },

  // ============================================================================
  // ALIAS FEATURES
  // ============================================================================
  'alias:read': {
    requiredScope: 'aliases:read',
    requiredPlanPermissions: [],
    description: 'Read alias information - available on all plans'
  },
  
  'alias:create': {
    requiredScope: 'aliases:write',
    requiredPlanPermissions: [],
    resourceLimits: { resourceType: 'aliases', operation: 'create' },
    description: 'Create alias - available on all plans (subject to quantity limits)'
  },
  
  'alias:update': {
    requiredScope: 'aliases:write',
    requiredPlanPermissions: [],
    description: 'Update alias - available on all plans'
  },
  
  'alias:delete': {
    requiredScope: 'aliases:write',
    requiredPlanPermissions: [],
    description: 'Delete alias - available on all plans'
  },

  // ============================================================================
  // ANALYTICS FEATURES (Enterprise only)
  // ============================================================================
  'analytics:read': {
    requiredScope: 'analytics:read',
    requiredPlanPermissions: ['email_analytics'],
    minimumPlan: 'enterprise',
    description: 'View email analytics dashboard - requires Enterprise plan'
  },

  // ============================================================================
  // PROFILE/ACCOUNT FEATURES
  // ============================================================================
  'profile:read': {
    requiredScope: 'profile:read',
    requiredPlanPermissions: [],
    description: 'Read own profile information - available on all plans'
  },
  
  'profile:update': {
    requiredScope: 'profile:write',
    requiredPlanPermissions: [],
    description: 'Update own profile information - available on all plans'
  },

  // ============================================================================
  // API KEY FEATURES  
  // ============================================================================
  'apikey:read': {
    requiredScope: 'apikeys:read',
    requiredPlanPermissions: [],
    description: 'Read own API keys - available on all plans'
  },
  
  'apikey:create': {
    requiredScope: 'apikeys:write',
    requiredPlanPermissions: [],
    description: 'Create API keys - available on all plans'
  },
  
  'apikey:delete': {
    requiredScope: 'apikeys:write',
    requiredPlanPermissions: [],
    description: 'Delete own API keys - available on all plans'
  },

  // ============================================================================
  // BILLING FEATURES
  // ============================================================================
  'billing:read': {
    requiredScope: 'billing:read',
    requiredPlanPermissions: [],
    description: 'Read billing information - available on all plans'
  },
  
  'billing:update': {
    requiredScope: 'billing:write',
    requiredPlanPermissions: [],
    description: 'Update billing/plan information - available on all plans'
  },

  // ============================================================================
  // BASIC AUTH (no specific permissions)
  // ============================================================================
  'auth:basic': {
    requiredScope: '',
    requiredPlanPermissions: [],
    description: 'Basic authentication - no specific permissions required'
  },

  // ============================================================================
  // ADMIN FEATURES
  // ============================================================================
  'admin:access': {
    requiredScope: 'admin:*',
    requiredPlanPermissions: [],
    description: 'Admin panel access - admin authentication required'
  }
};

/**
 * Helper function to get feature config
 */
export function getFeatureConfig(featureName: string): FeatureConfig | null {
  return FEATURE_CONFIGS[featureName] || null;
}

/**
 * Helper function to check if a feature exists
 */
export function isValidFeature(featureName: string): boolean {
  return featureName in FEATURE_CONFIGS;
}

/**
 * Get all available features for documentation/UI
 */
export function getAllFeatures(): Record<string, FeatureConfig> {
  return FEATURE_CONFIGS;
}

/**
 * Get features by category for organization
 */
export function getFeaturesByCategory(): Record<string, Record<string, FeatureConfig>> {
  const categories: Record<string, Record<string, FeatureConfig>> = {
    webhooks: {},
    domains: {},
    aliases: {},
    analytics: {},
    profile: {},
    apikeys: {},
    billing: {},
    admin: {}
  };

  Object.entries(FEATURE_CONFIGS).forEach(([featureName, config]) => {
    const category = featureName.split(':')[0];
    if (categories[category]) {
      categories[category][featureName] = config;
    }
  });

  return categories;
}

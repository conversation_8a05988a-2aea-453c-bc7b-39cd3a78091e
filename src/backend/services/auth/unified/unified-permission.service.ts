/**
 * UPDATED: Unified Permission Validation Service
 * 
 * Now uses the clear feature configuration system.
 * All permission logic is centralized and easy to understand.
 */

import { FastifyRequest } from 'fastify';
import { logger } from '../../../utils/logger.js';
import { PlanConfigService, PlanPermission, PlanType } from '../../billing/plan-config.service.js';
import { ApiKeyService } from '../api-key.service.js';
import { prisma } from '../../../lib/prisma.js';
import { getFeatureConfig, isValidFeature } from './feature-configs.js';
import {
  UnifiedPermissionRequest,
  UnifiedPermissionResult,
  FeaturePermissionRequest,
  EffectivePermissions,
  ResourceUsage,
  ResourceLimits
} from '../../../types/permissions.types.js';

export class UnifiedPermissionService {
  private static readonly apiKeyService = new ApiKeyService();

  /**
   * Validate permissions for a specific feature
   * This is the main entry point - everything goes through features now
   */
  static async validateFeatureAccess(request: FeaturePermissionRequest): Promise<UnifiedPermissionResult> {
    try {
      logger.debug({
        userId: request.userId,
        featureName: request.featureName,
        hasApiKey: !!request.apiKey
      }, 'Starting feature permission validation');

      // Check if feature exists
      const featureConfig = getFeatureConfig(request.featureName);
      if (!featureConfig) {
        return this.createErrorResult(`Unknown feature: ${request.featureName}`, 'free');
      }

      // Special case: basic auth requires no permissions
      if (request.featureName === 'auth:basic') {
        return {
          allowed: true,
          details: {
            hasApiScope: true,
            hasPlanPermission: true,
            withinLimits: true,
            currentPlan: 'free' as PlanType,
            upgradeUrl: '/settings#billing'
          }
        };
      }

      // Get user information
      const user = await this.getUserWithPlan(request.userId);
      if (!user) {
        return this.createErrorResult('User not found', 'free');
      }

      const result: UnifiedPermissionResult = {
        allowed: false,
        reason: '',
        details: {
          hasApiScope: false,
          hasPlanPermission: false,
          withinLimits: false,
          currentPlan: user.planType as PlanType,
          upgradeUrl: '/settings#billing'
        }
      };

      // 1. Validate API Scope (if API key is provided and scope is required)
      if (request.apiKey && featureConfig.requiredScope) {
        const scopeValidation = await this.validateApiScope(request.apiKey, featureConfig.requiredScope);
        result.details.hasApiScope = scopeValidation.valid;
        result.details.scopeReason = scopeValidation.reason;
        
        if (!scopeValidation.valid) {
          result.reason = scopeValidation.reason || 'Insufficient API scope';
          return result;
        }
      } else {
        // No API key means browser request, or no scope required
        result.details.hasApiScope = true;
      }

      // 2. Validate Plan Permissions (if required)
      if (featureConfig.requiredPlanPermissions.length > 0) {
        const hasAllPermissions = featureConfig.requiredPlanPermissions.every(permission =>
          PlanConfigService.userHasPermission(user.planType, permission)
        );
        result.details.hasPlanPermission = hasAllPermissions;
        
        if (!hasAllPermissions) {
          const missingPermissions = featureConfig.requiredPlanPermissions.filter(permission =>
            !PlanConfigService.userHasPermission(user.planType, permission)
          );
          
          result.details.requiredPlan = featureConfig.minimumPlan;
          result.details.upgradeRequired = true;
          result.details.planPermissionReason = 
            `Feature requires ${missingPermissions.join(', ')} permission(s) (${featureConfig.minimumPlan}+ plan)`;
          result.reason = result.details.planPermissionReason;
          return result;
        }
      } else {
        result.details.hasPlanPermission = true;
      }

      // 3. Validate Resource Limits (if specified)
      if (featureConfig.resourceLimits) {
        const limitValidation = await this.validateResourceLimits(
          user,
          featureConfig.resourceLimits
        );
        result.details.withinLimits = limitValidation.valid;
        result.details.limitReason = limitValidation.reason;
        result.details.currentUsage = limitValidation.currentUsage;
        result.details.limits = limitValidation.limits;
        
        if (!limitValidation.valid) {
          result.details.upgradeRequired = true;
          result.reason = limitValidation.reason || 'Resource limit exceeded';
          return result;
        }
      } else {
        result.details.withinLimits = true;
      }

      // 4. Check minimum plan requirement (if specified)
      if (featureConfig.minimumPlan && !this.meetsPlanRequirement(user.planType as PlanType, featureConfig.minimumPlan)) {
        result.details.requiredPlan = featureConfig.minimumPlan;
        result.details.upgradeRequired = true;
        result.reason = `Feature requires ${featureConfig.minimumPlan}+ plan`;
        return result;
      }

      // All validations passed
      result.allowed = true;
      result.reason = undefined;

      logger.debug({
        userId: request.userId,
        featureName: request.featureName,
        allowed: result.allowed,
        details: result.details
      }, 'Feature permission validation completed');

      return result;
    } catch (error) {
      logger.error({
        userId: request.userId,
        featureName: request.featureName,
        error: error instanceof Error ? error.message : error
      }, 'Feature permission validation failed');
      
      return this.createErrorResult('Permission validation failed', 'free');
    }
  }

  /**
   * Legacy method for backward compatibility (converts to feature-based)
   */
  static async validatePermission(request: UnifiedPermissionRequest): Promise<UnifiedPermissionResult> {
    // Convert legacy request to feature-based
    const featureRequest: FeaturePermissionRequest = {
      userId: request.userId,
      apiKey: request.apiKey,
      featureName: request.featureName || this.scopeToFeature(request.requiredScope)
    };

    return this.validateFeatureAccess(featureRequest);
  }

  /**
   * Get effective permissions for a user
   */
  static async getEffectivePermissions(userId: string, apiKey?: string): Promise<EffectivePermissions> {
    try {
      const user = await this.getUserWithPlan(userId);
      if (!user) {
        throw new Error('User not found');
      }

      let scopes: string[] = [];
      
      // Get API scopes if API key provided
      if (apiKey) {
        const keyValidation = await this.apiKeyService.verifyApiKey(apiKey);
        if (keyValidation.success) {
          scopes = keyValidation.scopes || [];
        }
      } else {
        // For browser requests, assume all scopes available based on plan
        scopes = this.getAllAvailableScopes(user.planType as PlanType);
      }

      const planPermissions = PlanConfigService.getPlanPermissions(user.planType);
      const currentUsage = await this.getCurrentUsage(userId);
      const limits = PlanConfigService.getPlanLimits(user.planType, currentUsage.domains);

      // Convert plan limits to our ResourceLimits type
      const resourceLimits: ResourceLimits = {
        domains: limits.domains,
        aliases: limits.aliases,
        webhooks: limits.webhooks,
        emails: limits.emails,
        monthlyEmails: limits.monthlyEmails
      };

      // Get available features based on scopes and plan permissions
      const availableFeatures = await this.getAvailableFeatures(userId, scopes, planPermissions);
      const restrictions = this.getRestrictions(user.planType as PlanType, currentUsage, resourceLimits);

      return {
        scopes,
        planPermissions: [...planPermissions],
        planType: user.planType as PlanType,
        resourceLimits,
        currentUsage,
        availableFeatures,
        restrictions
      };
    } catch (error) {
      logger.error({ userId, error }, 'Failed to get effective permissions');
      throw error;
    }
  }

  /**
   * Private helper methods
   */

  private static async getUserWithPlan(userId: string) {
    return prisma.user.findUnique({
      where: { id: userId },
      select: { 
        id: true, 
        email: true, 
        planType: true,
        currentMonthEmails: true,
        monthlyEmailLimit: true
      }
    });
  }

  private static async validateApiScope(apiKey: string, requiredScope: string): Promise<{ valid: boolean; reason?: string }> {
    const keyValidation = await this.apiKeyService.verifyApiKey(apiKey);
    if (!keyValidation.success) {
      return { valid: false, reason: 'Invalid API key' };
    }

    const scopes = keyValidation.scopes || [];
    const hasScope = this.checkScopePermission(scopes, requiredScope);
    
    return {
      valid: hasScope,
      reason: hasScope ? undefined : `Missing required scope: ${requiredScope}`
    };
  }

  private static checkScopePermission(userScopes: string[], requiredScope: string): boolean {
    // Full access wildcard
    if (userScopes.includes('*')) return true;
    
    // Direct match
    if (userScopes.includes(requiredScope)) return true;
    
    // Wildcard match (e.g., "webhooks:*" allows "webhooks:write")
    const [resource] = requiredScope.split(':');
    if (userScopes.includes(`${resource}:*`)) return true;
    
    return false;
  }

  private static async validateResourceLimits(user: any, limitCheck: any): Promise<{ valid: boolean; reason?: string; currentUsage?: ResourceUsage; limits?: ResourceLimits }> {
    const currentUsage = await this.getCurrentUsage(user.id);
    const limits = PlanConfigService.getPlanLimits(user.planType, currentUsage.domains);

    const resourceLimits: ResourceLimits = {
      domains: limits.domains,
      aliases: limits.aliases,
      webhooks: limits.webhooks,
      emails: limits.emails,
      monthlyEmails: limits.monthlyEmails
    };

    // Check specific resource type
    const currentCount = currentUsage[limitCheck.resourceType];
    const limit = resourceLimits[limitCheck.resourceType];
    const quantity = limitCheck.quantity || 1;

    if (limitCheck.operation === 'create') {
      const wouldExceed = (currentCount + quantity) > limit;
      if (wouldExceed) {
        return {
          valid: false,
          reason: `Cannot create ${limitCheck.resourceType}. Plan limit of ${limit} reached (${currentCount}/${limit}).`,
          currentUsage,
          limits: resourceLimits
        };
      }
    }

    return {
      valid: true,
      currentUsage,
      limits: resourceLimits
    };
  }

  private static async getCurrentUsage(userId: string): Promise<ResourceUsage> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        _count: {
          select: {
            domains: true,
            webhooks: true
          }
        }
      }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Get alias count
    const aliasCount = await prisma.alias.count({
      where: {
        domain: { userId }
      }
    });

    return {
      domains: user._count.domains,
      webhooks: user._count.webhooks,
      aliases: aliasCount,
      emails: user.currentMonthEmails,
      monthlyEmails: user.currentMonthEmails
    };
  }

  private static meetsPlanRequirement(currentPlan: PlanType, requiredPlan: PlanType): boolean {
    const hierarchy: PlanType[] = ['free', 'pro', 'enterprise'];
    const currentIndex = hierarchy.indexOf(currentPlan);
    const requiredIndex = hierarchy.indexOf(requiredPlan);
    return currentIndex >= requiredIndex;
  }

  private static getAllAvailableScopes(planType: PlanType): string[] {
    // Return all possible scopes - in browser requests, we don't restrict by API key
    return [
      'domains:read', 'domains:write', 'domains:config', 'domains:status', 'domains:*',
      'aliases:read', 'aliases:write', 'aliases:*',
      'webhooks:read', 'webhooks:write', 'webhooks:*',
      'analytics:read',
      'profile:read', 'profile:write',
      'apikeys:read', 'apikeys:write',
      'billing:read', 'billing:write'
    ];
  }

  private static async getAvailableFeatures(userId: string, scopes: string[], planPermissions: readonly PlanPermission[]): Promise<string[]> {
    const availableFeatures: string[] = [];
    const { getAllFeatures } = await import('./feature-configs.js');
    const allFeatures = getAllFeatures();
    
    for (const [featureName, config] of Object.entries(allFeatures)) {
      // Check if user has required scope
      const hasScope = !config.requiredScope || this.checkScopePermission(scopes, config.requiredScope);
      
      // Check if user has required plan permissions
      const hasPermissions = config.requiredPlanPermissions.every(p => planPermissions.includes(p));
      
      if (hasScope && hasPermissions) {
        // For resource-limited features, check if user is within limits
        if (config.resourceLimits) {
          try {
            const currentUsage = await this.getCurrentUsage(userId);
            const limits = PlanConfigService.getPlanLimits('free', currentUsage.domains); // Use actual plan type in real implementation
            const currentCount = currentUsage[config.resourceLimits.resourceType];
            const limit = limits[config.resourceLimits.resourceType];
            
            if (currentCount < limit) {
              availableFeatures.push(featureName);
            }
          } catch (error) {
            // Skip this feature if we can't check limits
            continue;
          }
        } else {
          availableFeatures.push(featureName);
        }
      }
    }
    
    return availableFeatures;
  }

  private static getRestrictions(planType: PlanType, usage: ResourceUsage, limits: ResourceLimits): string[] {
    const restrictions: string[] = [];
    
    // Check usage against limits
    Object.keys(limits).forEach(resource => {
      const resourceKey = resource as keyof ResourceLimits;
      if (usage[resourceKey] >= limits[resourceKey]) {
        restrictions.push(`${resource} limit reached (${usage[resourceKey]}/${limits[resourceKey]})`);
      }
    });
    
    // Add plan-specific restrictions
    if (planType === 'free') {
      restrictions.push('Limited to basic features on Free plan');
    }
    
    return restrictions;
  }

  private static createErrorResult(reason: string, planType: PlanType): UnifiedPermissionResult {
    return {
      allowed: false,
      reason,
      details: {
        hasApiScope: false,
        hasPlanPermission: false,
        withinLimits: false,
        currentPlan: planType,
        upgradeUrl: '/settings#billing'
      }
    };
  }

  private static scopeToFeature(scope: string): string {
    // Convert legacy scope to feature name
    const scopeToFeatureMap: Record<string, string> = {
      'webhooks:read': 'webhook:read',
      'webhooks:write': 'webhook:create',
      'domains:read': 'domain:read',
      'domains:write': 'domain:create',
      'domains:config': 'domain:config',
      'aliases:read': 'alias:read',
      'aliases:write': 'alias:create',
      'analytics:read': 'analytics:read',
      'profile:read': 'profile:read',
      'profile:write': 'profile:update',
      'apikeys:read': 'apikey:read',
      'apikeys:write': 'apikey:create',
      'billing:read': 'billing:read',
      'billing:write': 'billing:update'
    };

    return scopeToFeatureMap[scope] || 'auth:basic';
  }
}

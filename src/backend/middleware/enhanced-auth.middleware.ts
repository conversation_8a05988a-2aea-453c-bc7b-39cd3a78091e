/**
 * Enhanced Permission Middleware
 * 
 * This middleware provides additional helper functions for complex permission scenarios
 * that go beyond the basic auth() function. It works in conjunction with the unified
 * authentication system.
 */

import { FastifyRequest, FastifyReply } from 'fastify';
import { auth } from '../lib/auth.js';
import { UnifiedPermissionService } from '../services/auth/unified/unified-permission.service.js';
import { logger } from '../utils/logger.js';

/**
 * Conditional permission middleware
 * Applies different permission requirements based on request content
 */
export function conditionalAuth(conditions: {
  [key: string]: { feature?: string; scope?: string; planPermission?: string }
}) {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    // First ensure user is authenticated
    await auth({})(request, reply);
    
    // Check if response was already sent (auth failed)
    if (reply.sent) return;
    
    const user = (request as any).user;
    if (!user) return;

    // Determine which condition applies based on request body/params
    const body = request.body as any;
    let appliedCondition: any = null;
    
    for (const [conditionKey, requirements] of Object.entries(conditions)) {
      if (checkCondition(conditionKey, request, body)) {
        appliedCondition = requirements;
        break;
      }
    }

    if (!appliedCondition) {
      return; // No specific conditions apply, basic auth is sufficient
    }

    // Apply the conditional permission check
    if (appliedCondition.feature) {
      const result = await UnifiedPermissionService.validateFeatureAccess({
        userId: user.id,
        apiKey: (request as any).apiKey,
        featureName: appliedCondition.feature
      });
      
      if (!result.allowed) {
        return sendPermissionError(reply, result);
      }
    }
  };
}

/**
 * Batch operation permission middleware
 * Validates permissions for operations that affect multiple resources
 */
export function batchAuth(options: {
  maxBatchSize?: number;
  feature: string;
  resourceType: 'domains' | 'aliases' | 'webhooks';
}) {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    // Apply basic feature auth first
    await auth({ feature: options.feature })(request, reply);
    
    if (reply.sent) return;

    const body = request.body as any;
    const batchSize = Array.isArray(body) ? body.length : 
                     body.items ? body.items.length : 1;

    // Check batch size limits
    if (options.maxBatchSize && batchSize > options.maxBatchSize) {
      return reply.status(400).send({
        statusCode: 400,
        error: 'Bad Request',
        message: `Batch size ${batchSize} exceeds maximum of ${options.maxBatchSize}`
      });
    }

    // Check if user has enough quota for batch operation
    const user = (request as any).user;
    const result = await UnifiedPermissionService.validatePermission({
      userId: user.id,
      apiKey: (request as any).apiKey,
      requiredScope: `${options.resourceType}:write`,
      resourceLimits: {
        resourceType: options.resourceType,
        operation: 'create',
        quantity: batchSize
      }
    });

    if (!result.allowed) {
      return sendPermissionError(reply, result);
    }
  };
}

/**
 * Context-aware permission middleware
 * Validates permissions based on resource ownership and context
 */
export function contextAuth(options: {
  resourceType: 'domain' | 'alias' | 'webhook';
  operation: 'read' | 'write' | 'delete';
  allowCrossUserAccess?: boolean; // For admin operations
}) {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    // Apply basic auth first
    const scope = `${options.resourceType}s:${options.operation === 'delete' ? 'write' : options.operation}`;
    await auth({ scope })(request, reply);
    
    if (reply.sent) return;

    const user = (request as any).user;
    const resourceId = (request.params as any)[`${options.resourceType}Id`];

    if (!resourceId) {
      return reply.status(400).send({
        statusCode: 400,
        error: 'Bad Request',
        message: `${options.resourceType} ID is required`
      });
    }

    // Verify resource ownership (unless cross-user access is allowed)
    if (!options.allowCrossUserAccess) {
      const ownsResource = await checkResourceOwnership(
        user.id, 
        options.resourceType, 
        resourceId
      );

      if (!ownsResource) {
        return reply.status(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: `${options.resourceType} not found`
        });
      }
    }
  };
}

/**
 * Rate limiting auth middleware
 * Combines authentication with rate limiting based on plan type
 */
export function rateLimitedAuth(options: {
  feature?: string;
  scope?: string;
  rateLimit: {
    free: number;
    pro: number;
    enterprise: number;
    window: number; // in seconds
  };
}) {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    // Apply basic auth
    const authOptions = options.feature ? { feature: options.feature } : { scope: options.scope };
    await auth(authOptions)(request, reply);
    
    if (reply.sent) return;

    const user = (request as any).user;
    const planType = user.planType as 'free' | 'pro' | 'enterprise';
    const userLimit = options.rateLimit[planType];
    
    // Check rate limit (this would integrate with your rate limiting system)
    const isWithinLimit = await checkRateLimit(user.id, userLimit, options.rateLimit.window);
    
    if (!isWithinLimit) {
      return reply.status(429).send({
        statusCode: 429,
        error: 'Too Many Requests',
        message: `Rate limit exceeded. ${userLimit} requests per ${options.rateLimit.window} seconds for ${planType} plan.`,
        upgradeUrl: planType === 'free' ? '/settings#billing' : undefined
      });
    }
  };
}

/**
 * Helper functions
 */

function checkCondition(conditionKey: string, request: FastifyRequest, body: any): boolean {
  switch (conditionKey) {
    case 'hasCustomHeaders':
      return body.headers && Object.keys(body.headers).length > 0;
    case 'hasAdvancedConfig':
      return body.configuration && (
        body.configuration.allowAttachments !== undefined ||
        body.configuration.includeEnvelopeData !== undefined
      );
    case 'isBulkOperation':
      return Array.isArray(body) || (body.items && Array.isArray(body.items));
    default:
      return false;
  }
}

async function checkResourceOwnership(
  userId: string, 
  resourceType: string, 
  resourceId: string
): Promise<boolean> {
  try {
    const { PrismaClient } = await import('@prisma/client');
    const prisma = new PrismaClient();

    let query;
    switch (resourceType) {
      case 'domain':
        query = prisma.domain.findFirst({
          where: { id: resourceId, userId },
          select: { id: true }
        });
        break;
      case 'webhook':
        query = prisma.webhook.findFirst({
          where: { id: resourceId, userId },
          select: { id: true }
        });
        break;
      case 'alias':
        query = prisma.alias.findFirst({
          where: { 
            id: resourceId,
            domain: { userId }
          },
          select: { id: true }
        });
        break;
      default:
        return false;
    }

    const resource = await query;
    await prisma.$disconnect();
    
    return !!resource;
  } catch (error) {
    logger.error({ error, userId, resourceType, resourceId }, 'Error checking resource ownership');
    return false;
  }
}

async function checkRateLimit(userId: string, limit: number, windowSeconds: number): Promise<boolean> {
  // This would integrate with your rate limiting system (Redis, etc.)
  // For now, return true (no rate limiting implemented)
  return true;
}

function sendPermissionError(reply: FastifyReply, result: any) {
  const { details } = result;
  const statusCode = details.upgradeRequired ? 402 : 403;

  return reply.status(statusCode).send({
    statusCode,
    error: statusCode === 402 ? 'Payment Required' : 'Forbidden',
    message: result.reason,
    success: false,
    details: {
      errorType: !details.hasApiScope ? 'scope_missing' :
                !details.hasPlanPermission ? 'plan_permission_missing' :
                !details.withinLimits ? 'limit_exceeded' : 'feature_not_available',
      currentPlan: details.currentPlan,
      requiredPlan: details.requiredPlan,
      upgradeRequired: details.upgradeRequired
    },
    upgradeUrl: details.upgradeUrl
  });
}

/**
 * Pre-configured middleware for common scenarios
 */

// Webhook creation with custom headers detection
export const smartWebhookAuth = conditionalAuth({
  hasCustomHeaders: { feature: 'create_webhook' },        // Requires Pro+
  default: { feature: 'create_basic_webhook' }            // Free plan OK
});

// Domain config with advanced features detection  
export const smartDomainAuth = conditionalAuth({
  hasAdvancedConfig: { feature: 'update_domain_config' }, // Requires Pro+
  default: { scope: 'domains:write' }                     // Basic updates
});

// Batch operations with size limits
export const batchWebhookAuth = batchAuth({
  maxBatchSize: 10,
  feature: 'create_webhook',
  resourceType: 'webhooks'
});

// Context-aware resource access
export const domainOwnerAuth = contextAuth({
  resourceType: 'domain',
  operation: 'write'
});

export const webhookOwnerAuth = contextAuth({
  resourceType: 'webhook', 
  operation: 'write'
});

export const aliasOwnerAuth = contextAuth({
  resourceType: 'alias',
  operation: 'write'
});

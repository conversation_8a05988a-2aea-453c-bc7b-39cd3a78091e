/**
 * Unified Authentication Middleware
 * 
 * This middleware provides unified authentication and permission validation,
 * combining API key authentication, scope validation, and plan permission checks
 * in a single middleware that can be easily applied to any route.
 */

import { FastifyRequest, FastifyReply } from 'fastify';
import { UnifiedPermissionService } from '../services/auth/unified/unified-permission.service.js';
import { UserAuthService } from '../services/auth/user-auth.service.js';
import { ApiKeyService } from '../services/auth/api-key.service.js';
import { logger } from '../utils/logger.js';
import { 
  UnifiedPermissionRequest, 
  FeaturePermissionRequest,
  ResourceLimitCheck,
  PermissionError 
} from '../types/permissions.types.js';
import { PlanPermission } from '../services/billing/plan-config.service.js';

export interface UnifiedAuthOptions {
  // Basic scope requirement
  requiredScope?: string;
  
  // Plan permission requirement
  requiredPlanPermission?: PlanPermission;
  
  // Feature-based validation (alternative to scope + permission)
  requiredFeature?: string;
  
  // Resource limit validation
  resourceLimits?: ResourceLimitCheck;
  
  // Allow unauthenticated access (for public routes)
  allowUnauthenticated?: boolean;
  
  // Custom error messages
  errorMessages?: {
    scope?: string;
    planPermission?: string;
    limits?: string;
  };
}

export class UnifiedAuthMiddleware {
  private static readonly userAuthService = new UserAuthService();
  private static readonly apiKeyService = new ApiKeyService();

  /**
   * Create unified authentication middleware with specific requirements
   */
  static create(options: UnifiedAuthOptions = {}) {
    return async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        // 1. Authenticate user (API key or session cookie)
        const authResult = await this.authenticateUser(request);
        
        if (!authResult.success) {
          if (options.allowUnauthenticated) {
            return; // Continue without authentication
          }
          return this.sendAuthError(reply, authResult.error!, request);
        }

        // Attach user info to request
        (request as any).user = authResult.user;
        (request as any).authMethod = authResult.method;
        (request as any).apiKey = authResult.apiKey;

        // 2. Skip permission validation if no requirements specified
        if (!options.requiredScope && !options.requiredPlanPermission && !options.requiredFeature && !options.resourceLimits) {
          return; // Only authentication required
        }

        // 3. Validate permissions using unified service
        let permissionResult;
        
        if (options.requiredFeature) {
          // Feature-based validation
          const featureRequest: FeaturePermissionRequest = {
            userId: authResult.user!.id,
            apiKey: authResult.apiKey,
            featureName: options.requiredFeature
          };
          permissionResult = await UnifiedPermissionService.validateFeatureAccess(featureRequest);
        } else {
          // Scope + permission based validation
          const permissionRequest: UnifiedPermissionRequest = {
            userId: authResult.user!.id,
            apiKey: authResult.apiKey,
            requiredScope: options.requiredScope || '',
            requiredPlanPermission: options.requiredPlanPermission,
            resourceLimits: options.resourceLimits
          };
          permissionResult = await UnifiedPermissionService.validatePermission(permissionRequest);
        }

        if (!permissionResult.allowed) {
          return this.sendPermissionError(reply, permissionResult, options);
        }

        // Attach permission context to request for use in controllers
        (request as any).permissions = {
          validated: true,
          details: permissionResult.details,
          effectivePermissions: await UnifiedPermissionService.getEffectivePermissions(
            authResult.user!.id,
            authResult.apiKey
          )
        };

        logger.debug({
          userId: authResult.user!.id,
          authMethod: authResult.method,
          requiredScope: options.requiredScope,
          requiredFeature: options.requiredFeature,
          allowed: true
        }, 'Unified authentication and permission validation passed');

      } catch (error) {
        logger.error({
          error: error instanceof Error ? error.message : error,
          url: request.url,
          method: request.method
        }, 'Unified auth middleware error');
        
        return reply.status(500).send({
          statusCode: 500,
          error: 'Internal Server Error',
          message: 'Authentication system error'
        });
      }
    };
  }

  /**
   * Authenticate user via API key or session cookie
   */
  private static async authenticateUser(request: FastifyRequest): Promise<{
    success: boolean;
    user?: { id: string; email: string; planType: string };
    method?: 'api_key' | 'session';
    apiKey?: string;
    error?: string;
  }> {
    const apiKey = request.headers['x-api-key'] as string;
    
    // Try API key authentication first
    if (apiKey) {
      const keyResult = await this.apiKeyService.verifyApiKey(apiKey);
      if (keyResult.success) {
        return {
          success: true,
          user: keyResult.user!,
          method: 'api_key',
          apiKey
        };
      }
      return { success: false, error: 'Invalid API key' };
    }

    // Try session cookie authentication
    const authHeader = request.headers.authorization as string;
    const bearerToken = authHeader?.startsWith('Bearer ') ? authHeader.slice(7) : null;
    const cookieToken = request.cookies.user_token;
    const token = bearerToken || cookieToken;

    if (!token) {
      return { success: false, error: 'No authentication provided' };
    }

    const verifyResult = this.userAuthService.verifyToken(token);
    if (!verifyResult.success) {
      return { success: false, error: 'Invalid session token' };
    }

    // Verify user still exists and get current plan
    try {
      const { PrismaClient } = await import('@prisma/client');
      const prisma = new PrismaClient();

      const user = await prisma.user.findUnique({
        where: { id: verifyResult.payload!.userId },
        select: { id: true, email: true, planType: true }
      });

      await prisma.$disconnect();

      if (!user) {
        return { success: false, error: 'User not found' };
      }

      return {
        success: true,
        user,
        method: 'session'
      };
    } catch (error) {
      logger.error({ error }, 'Database error during user verification');
      return { success: false, error: 'Database error' };
    }
  }

  /**
   * Send authentication error response
   */
  private static sendAuthError(reply: FastifyReply, error: string, request: FastifyRequest) {
    // Handle redirects for browser requests to dashboard areas
    if (request.url.startsWith('/domains') && request.method !== 'POST') {
      return reply.status(302).redirect('/login');
    }

    return reply.status(401).send({
      statusCode: 401,
      error: 'Unauthorized',
      message: error
    });
  }

  /**
   * Send permission error with detailed information
   */
  private static sendPermissionError(
    reply: FastifyReply, 
    permissionResult: any, 
    options: UnifiedAuthOptions
  ) {
    const details = permissionResult.details;
    const statusCode = details.upgradeRequired ? 402 : 403; // 402 for payment required, 403 for forbidden

    // Determine primary error type and message
    let errorType: string;
    let message: string;

    if (!details.hasApiScope) {
      errorType = 'scope_missing';
      message = options.errorMessages?.scope || details.scopeReason || 'Insufficient API scope';
    } else if (!details.hasPlanPermission) {
      errorType = 'plan_permission_missing';
      message = options.errorMessages?.planPermission || details.planPermissionReason || 'Plan upgrade required';
    } else if (!details.withinLimits) {
      errorType = 'limit_exceeded';
      message = options.errorMessages?.limits || details.limitReason || 'Resource limit exceeded';
    } else {
      errorType = 'feature_not_available';
      message = permissionResult.reason || 'Feature not available';
    }

    const errorResponse = {
      statusCode,
      error: statusCode === 402 ? 'Payment Required' : 'Forbidden',
      message,
      success: false,
      details: {
        errorType,
        hasApiScope: details.hasApiScope,
        hasPlanPermission: details.hasPlanPermission,
        withinLimits: details.withinLimits,
        currentPlan: details.currentPlan,
        requiredPlan: details.requiredPlan,
        upgradeRequired: details.upgradeRequired,
        currentUsage: details.currentUsage,
        limits: details.limits
      },
      upgradeUrl: details.upgradeUrl
    };

    logger.warn({
      errorType,
      currentPlan: details.currentPlan,
      requiredPlan: details.requiredPlan,
      upgradeRequired: details.upgradeRequired,
      message
    }, 'Permission validation failed');

    return reply.status(statusCode).send(errorResponse);
  }

  /**
   * Convenience methods for common permission patterns
   */

  // Basic authentication only
  static requireAuth() {
    return this.create({});
  }

  // Scope-based authentication
  static requireScope(scope: string, planPermission?: PlanPermission) {
    return this.create({
      requiredScope: scope,
      requiredPlanPermission: planPermission
    });
  }

  // Feature-based authentication
  static requireFeature(featureName: string) {
    return this.create({
      requiredFeature: featureName
    });
  }

  // Resource creation with limits
  static requireResourceCreation(resourceType: 'domains' | 'aliases' | 'webhooks', scope: string, planPermission?: PlanPermission) {
    return this.create({
      requiredScope: scope,
      requiredPlanPermission: planPermission,
      resourceLimits: {
        resourceType,
        operation: 'create'
      }
    });
  }

  // Plan permission only
  static requirePlanPermission(permission: PlanPermission) {
    return this.create({
      requiredPlanPermission: permission
    });
  }
}

/**
 * Convenience middleware functions for easy import
 */

// Basic auth
export const requireAuth = UnifiedAuthMiddleware.requireAuth;

// Webhook permissions
export const requireWebhookRead = () => UnifiedAuthMiddleware.requireScope('webhooks:read');
export const requireWebhookWrite = () => UnifiedAuthMiddleware.requireScope('webhooks:write');
export const requireWebhookCreate = () => UnifiedAuthMiddleware.requireFeature('create_webhook');
export const requireBasicWebhookCreate = () => UnifiedAuthMiddleware.requireFeature('create_basic_webhook');

// Domain permissions
export const requireDomainRead = () => UnifiedAuthMiddleware.requireScope('domains:read');
export const requireDomainWrite = () => UnifiedAuthMiddleware.requireScope('domains:write');
export const requireDomainCreate = () => UnifiedAuthMiddleware.requireFeature('create_domain');
export const requireDomainConfig = () => UnifiedAuthMiddleware.requireFeature('update_domain_config');

// Alias permissions
export const requireAliasRead = () => UnifiedAuthMiddleware.requireScope('aliases:read');
export const requireAliasWrite = () => UnifiedAuthMiddleware.requireScope('aliases:write');
export const requireAliasCreate = () => UnifiedAuthMiddleware.requireFeature('create_alias');

// Analytics permissions
export const requireAnalytics = () => UnifiedAuthMiddleware.requireFeature('view_analytics');

// Plan-based permissions
export const requireProPlan = () => UnifiedAuthMiddleware.requirePlanPermission('custom_headers');
export const requireEnterprisePlan = () => UnifiedAuthMiddleware.requirePlanPermission('email_analytics');
